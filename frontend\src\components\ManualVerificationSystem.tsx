import React, { useState, useRef } from 'react';

interface ManualVerificationSystemProps {
  platform: string;
  taskTypes: string[];
  onVerificationComplete: (result: any) => void;
}

export const ManualVerificationSystem: React.FC<ManualVerificationSystemProps> = ({
  platform,
  taskTypes,
  onVerificationComplete
}) => {
  const [step, setStep] = useState<'instructions' | 'execution' | 'verification' | 'upload'>('instructions');
  const [screenshots, setScreenshots] = useState<File[]>([]);
  const [verificationResult, setVerificationResult] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const taskInstructions = {
    bilibili: {
      like: '1. 找到视频下方的点赞按钮（👍图标）\n2. 点击点赞按钮\n3. 确认按钮变为蓝色或显示已点赞状态',
      share: '1. 找到视频下方的分享按钮\n2. 点击分享按钮\n3. 在弹出的分享面板中选择任意分享方式',
      follow: '1. 找到UP主头像旁边的关注按钮\n2. 点击关注按钮\n3. 确认按钮变为"已关注"状态',
      comment: '1. 滚动到评论区\n2. 点击评论输入框\n3. 输入任意评论内容并发送'
    },
    douyin: {
      like: '1. 找到视频右侧的红心点赞按钮\n2. 点击红心按钮\n3. 确认红心变为红色并有动画效果',
      share: '1. 找到视频右侧的分享按钮\n2. 点击分享按钮\n3. 在弹出的分享面板中选择任意分享方式',
      follow: '1. 找到作者头像旁边的关注按钮\n2. 点击关注按钮\n3. 确认按钮变为"已关注"状态',
      comment: '1. 点击视频右侧的评论按钮\n2. 在评论输入框中输入内容\n3. 点击发送按钮'
    }
  };

  const handleStartExecution = () => {
    setStep('execution');
    
    // 打开目标网站
    const urls = {
      bilibili: 'https://www.bilibili.com',
      douyin: 'https://www.douyin.com',
      kuaishou: 'https://www.kuaishou.com',
      xiaohongshu: 'https://www.xiaohongshu.com'
    };
    
    const url = urls[platform as keyof typeof urls];
    if (url) {
      window.open(url, '_blank');
    }
    
    // 3分钟后自动进入验证步骤
    setTimeout(() => {
      setStep('verification');
    }, 180000);
  };

  const handleManualVerification = () => {
    setStep('verification');
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setScreenshots(prev => [...prev, ...files]);
  };

  const removeScreenshot = (index: number) => {
    setScreenshots(prev => prev.filter((_, i) => i !== index));
  };

  const analyzeScreenshots = async () => {
    if (screenshots.length === 0) {
      alert('请至少上传一张截图');
      return;
    }

    setIsAnalyzing(true);

    try {
      // 模拟AI分析过程
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 模拟分析结果
      const analysisResult = {
        detectedActions: taskTypes.map(task => ({
          type: task,
          confidence: 85 + Math.random() * 10,
          evidence: `在截图中检测到${task}操作的视觉证据`
        })),
        overallConfidence: 90,
        verificationPassed: true
      };

      setVerificationResult(analysisResult);
      
      // 调用完成回调
      onVerificationComplete({
        success: true,
        method: 'manual_verification',
        screenshots: screenshots.length,
        analysisResult,
        completedTasks: taskTypes
      });

    } catch (error) {
      console.error('截图分析失败:', error);
      alert('截图分析失败，请重试');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const confirmManualCompletion = () => {
    const confirmed = window.confirm(
      `请确认您已经完成了以下操作：\n\n${taskTypes.map(task => `• ${task}`).join('\n')}\n\n点击"确定"表示已完成，点击"取消"返回继续操作。`
    );

    if (confirmed) {
      onVerificationComplete({
        success: true,
        method: 'user_confirmation',
        completedTasks: taskTypes,
        confidence: 100
      });
    }
  };

  return (
    <div className="manual-verification-system">
      {step === 'instructions' && (
        <div className="step-content">
          <div className="step-header">
            <h3>📋 任务执行指南</h3>
            <p>请按照以下步骤完成 {platform} 平台的任务</p>
          </div>

          <div className="task-instructions">
            {taskTypes.map(task => (
              <div key={task} className="task-instruction">
                <h4>🎯 {task} 操作步骤：</h4>
                <pre className="instruction-text">
                  {taskInstructions[platform as keyof typeof taskInstructions]?.[task as keyof any] || 
                   `请在${platform}平台完成${task}操作`}
                </pre>
              </div>
            ))}
          </div>

          <div className="step-actions">
            <button className="btn btn-primary" onClick={handleStartExecution}>
              🚀 开始执行任务
            </button>
          </div>
        </div>
      )}

      {step === 'execution' && (
        <div className="step-content">
          <div className="step-header">
            <h3>⏱️ 正在执行任务</h3>
            <p>请在新打开的窗口中完成所需操作</p>
          </div>

          <div className="execution-status">
            <div className="status-item">
              <span className="status-label">目标平台:</span>
              <span className="status-value">{platform}</span>
            </div>
            <div className="status-item">
              <span className="status-label">需要完成:</span>
              <span className="status-value">{taskTypes.join(', ')}</span>
            </div>
            <div className="status-item">
              <span className="status-label">执行时间:</span>
              <span className="status-value">建议 2-5 分钟</span>
            </div>
          </div>

          <div className="execution-tips">
            <h4>💡 执行提示：</h4>
            <ul>
              <li>请确保在正确的平台上操作</li>
              <li>按照指南逐步完成每个任务</li>
              <li>完成后可以截图保存证据</li>
              <li>如遇问题可以重新开始</li>
            </ul>
          </div>

          <div className="step-actions">
            <button className="btn btn-success" onClick={handleManualVerification}>
              ✅ 我已完成操作
            </button>
            <button className="btn btn-secondary" onClick={() => setStep('instructions')}>
              🔄 重新开始
            </button>
          </div>
        </div>
      )}

      {step === 'verification' && (
        <div className="step-content">
          <div className="step-header">
            <h3>🔍 任务验证</h3>
            <p>请选择验证方式来确认任务完成</p>
          </div>

          <div className="verification-options">
            <div className="verification-option">
              <h4>方式1: 用户确认（推荐）</h4>
              <p>如果您确信已经完成了所有操作，可以直接确认</p>
              <button className="btn btn-success" onClick={confirmManualCompletion}>
                ✅ 确认已完成所有操作
              </button>
            </div>

            <div className="verification-option">
              <h4>方式2: 截图验证（更可靠）</h4>
              <p>上传完成操作的截图，系统将自动分析验证</p>
              <button className="btn btn-primary" onClick={() => setStep('upload')}>
                📸 上传截图验证
              </button>
            </div>
          </div>
        </div>
      )}

      {step === 'upload' && (
        <div className="step-content">
          <div className="step-header">
            <h3>📸 截图上传验证</h3>
            <p>请上传显示任务完成状态的截图</p>
          </div>

          <div className="upload-area">
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleFileUpload}
              style={{ display: 'none' }}
            />
            
            <div className="upload-zone" onClick={() => fileInputRef.current?.click()}>
              <div className="upload-icon">📁</div>
              <p>点击选择截图文件</p>
              <p className="upload-hint">支持 JPG, PNG 格式，可选择多个文件</p>
            </div>
          </div>

          {screenshots.length > 0 && (
            <div className="uploaded-files">
              <h4>已上传的截图：</h4>
              <div className="file-list">
                {screenshots.map((file, index) => (
                  <div key={index} className="file-item">
                    <span className="file-name">{file.name}</span>
                    <button 
                      className="remove-btn"
                      onClick={() => removeScreenshot(index)}
                    >
                      ❌
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="step-actions">
            <button 
              className="btn btn-primary"
              onClick={analyzeScreenshots}
              disabled={screenshots.length === 0 || isAnalyzing}
            >
              {isAnalyzing ? '🔍 分析中...' : '🔍 开始分析截图'}
            </button>
            <button className="btn btn-secondary" onClick={() => setStep('verification')}>
              🔙 返回选择验证方式
            </button>
          </div>

          {verificationResult && (
            <div className="analysis-result">
              <h4>📊 分析结果：</h4>
              <div className="result-summary">
                <div className="confidence-score">
                  置信度: {verificationResult.overallConfidence}%
                </div>
                <div className="verification-status">
                  {verificationResult.verificationPassed ? '✅ 验证通过' : '❌ 验证失败'}
                </div>
              </div>
              
              <div className="detected-actions">
                {verificationResult.detectedActions.map((action: any, index: number) => (
                  <div key={index} className="action-result">
                    <span className="action-type">{action.type}</span>
                    <span className="action-confidence">{action.confidence.toFixed(1)}%</span>
                    <span className="action-evidence">{action.evidence}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <style jsx>{`
        .manual-verification-system {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .step-content {
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .step-header {
          text-align: center;
          margin-bottom: 30px;
        }

        .step-header h3 {
          margin: 0 0 10px 0;
          color: #1f2937;
          font-size: 24px;
        }

        .step-header p {
          margin: 0;
          color: #6b7280;
          font-size: 16px;
        }

        .task-instructions {
          margin-bottom: 30px;
        }

        .task-instruction {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 15px;
        }

        .task-instruction h4 {
          margin: 0 0 10px 0;
          color: #1f2937;
        }

        .instruction-text {
          background: white;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          padding: 15px;
          margin: 0;
          font-family: inherit;
          font-size: 14px;
          line-height: 1.6;
          white-space: pre-line;
        }

        .execution-status {
          background: #f0f9ff;
          border: 1px solid #0ea5e9;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 20px;
        }

        .status-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        }

        .status-label {
          font-weight: 500;
          color: #374151;
        }

        .status-value {
          color: #0369a1;
          font-weight: 500;
        }

        .execution-tips {
          margin-bottom: 30px;
        }

        .execution-tips h4 {
          margin: 0 0 15px 0;
          color: #1f2937;
        }

        .execution-tips ul {
          margin: 0;
          padding-left: 20px;
        }

        .execution-tips li {
          margin-bottom: 8px;
          color: #6b7280;
        }

        .verification-options {
          display: grid;
          gap: 20px;
          margin-bottom: 30px;
        }

        .verification-option {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 20px;
        }

        .verification-option h4 {
          margin: 0 0 10px 0;
          color: #1f2937;
        }

        .verification-option p {
          margin: 0 0 15px 0;
          color: #6b7280;
        }

        .upload-area {
          margin-bottom: 20px;
        }

        .upload-zone {
          border: 2px dashed #d1d5db;
          border-radius: 8px;
          padding: 40px;
          text-align: center;
          cursor: pointer;
          transition: border-color 0.2s;
        }

        .upload-zone:hover {
          border-color: #3b82f6;
        }

        .upload-icon {
          font-size: 48px;
          margin-bottom: 15px;
        }

        .upload-hint {
          color: #6b7280;
          font-size: 14px;
          margin: 5px 0 0 0;
        }

        .uploaded-files {
          margin-bottom: 20px;
        }

        .uploaded-files h4 {
          margin: 0 0 15px 0;
          color: #1f2937;
        }

        .file-list {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .file-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: #f0f9ff;
          border: 1px solid #0ea5e9;
          border-radius: 6px;
          padding: 10px 15px;
        }

        .file-name {
          color: #0369a1;
          font-weight: 500;
        }

        .remove-btn {
          background: none;
          border: none;
          cursor: pointer;
          font-size: 14px;
        }

        .analysis-result {
          background: #f0fdf4;
          border: 1px solid #22c55e;
          border-radius: 8px;
          padding: 20px;
          margin-top: 20px;
        }

        .analysis-result h4 {
          margin: 0 0 15px 0;
          color: #1f2937;
        }

        .result-summary {
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;
        }

        .confidence-score {
          font-weight: 500;
          color: #059669;
        }

        .verification-status {
          font-weight: 500;
          color: #059669;
        }

        .detected-actions {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .action-result {
          display: grid;
          grid-template-columns: 100px 80px 1fr;
          gap: 15px;
          align-items: center;
          background: white;
          border-radius: 6px;
          padding: 10px 15px;
        }

        .action-type {
          font-weight: 500;
          color: #1f2937;
        }

        .action-confidence {
          color: #059669;
          font-weight: 500;
        }

        .action-evidence {
          color: #6b7280;
          font-size: 14px;
        }

        .step-actions {
          display: flex;
          gap: 15px;
          justify-content: center;
        }

        .btn {
          padding: 12px 24px;
          border: none;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .btn-primary {
          background: #3b82f6;
          color: white;
        }

        .btn-primary:hover {
          background: #2563eb;
        }

        .btn-primary:disabled {
          background: #9ca3af;
          cursor: not-allowed;
        }

        .btn-success {
          background: #10b981;
          color: white;
        }

        .btn-success:hover {
          background: #059669;
        }

        .btn-secondary {
          background: #6b7280;
          color: white;
        }

        .btn-secondary:hover {
          background: #4b5563;
        }
      `}</style>
    </div>
  );
};
