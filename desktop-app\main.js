const { app, BrowserWindow, ipcMain, session } = require('electron');
const path = require('path');

class TaskDetectorApp {
  constructor() {
    this.mainWindow = null;
    this.taskWindow = null;
    this.detectedActions = [];
    this.isDetecting = false;
  }

  createMainWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false // 关键：禁用web安全限制
      }
    });

    this.mainWindow.loadFile('index.html');
    
    // 开发模式下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.webContents.openDevTools();
    }
  }

  createTaskWindow(platform, url) {
    this.taskWindow = new BrowserWindow({
      width: 1000,
      height: 700,
      parent: this.mainWindow,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: false, // 关键：禁用web安全限制
        preload: path.join(__dirname, 'preload.js')
      }
    });

    // 注入检测脚本
    this.taskWindow.webContents.on('dom-ready', () => {
      this.injectDetectionScript(platform);
    });

    // 监听页面内的消息
    this.taskWindow.webContents.on('ipc-message', (event, channel, data) => {
      if (channel === 'task-action-detected') {
        this.handleActionDetected(data);
      }
    });

    this.taskWindow.loadURL(url);
    return this.taskWindow;
  }

  injectDetectionScript(platform) {
    const script = `
      (function() {
        console.log('🚀 桌面应用检测脚本已注入 - 平台: ${platform}');
        
        let detectedActions = [];
        let clickCount = 0;
        
        // 监听所有点击事件
        document.addEventListener('click', function(e) {
          clickCount++;
          
          const target = e.target;
          const action = detectActionType(target, '${platform}');
          
          if (action) {
            const actionData = {
              type: action,
              timestamp: Date.now(),
              element: target.tagName,
              className: target.className,
              text: target.textContent?.slice(0, 50),
              platform: '${platform}',
              confidence: calculateConfidence(target, action)
            };
            
            detectedActions.push(actionData);
            
            // 发送到主进程
            if (window.electronAPI) {
              window.electronAPI.sendTaskAction(actionData);
            }
            
            console.log('✅ 检测到操作:', actionData);
          }
        }, true);
        
        // 检测操作类型
        function detectActionType(element, platform) {
          const text = element.textContent?.toLowerCase() || '';
          const className = element.className?.toLowerCase() || '';
          const id = element.id?.toLowerCase() || '';
          
          // 平台特定检测规则
          const platformRules = {
            bilibili: {
              like: ['点赞', 'like', '赞', 'video-like', 'like-info'],
              share: ['分享', 'share', '转发', 'video-share', 'share-info'],
              follow: ['关注', 'follow', '订阅', 'follow-btn', 'subscribe-btn'],
              comment: ['评论', 'comment', '回复', 'reply-btn']
            },
            douyin: {
              like: ['点赞', 'like', '赞', 'digg-btn', 'heart-btn'],
              share: ['分享', 'share', '转发', 'share-btn', 'forward-btn'],
              follow: ['关注', 'follow', 'attention-btn', 'follow-btn'],
              comment: ['评论', 'comment', '回复', 'comment-btn']
            }
          };
          
          const rules = platformRules[platform] || platformRules.bilibili;
          
          for (const [actionType, keywords] of Object.entries(rules)) {
            for (const keyword of keywords) {
              if (text.includes(keyword) || className.includes(keyword) || id.includes(keyword)) {
                return actionType;
              }
            }
          }
          
          return null;
        }
        
        // 计算置信度
        function calculateConfidence(element, action) {
          let confidence = 50;
          
          const text = element.textContent?.toLowerCase() || '';
          const className = element.className?.toLowerCase() || '';
          
          if (text.includes(action)) confidence += 30;
          if (className.includes(action)) confidence += 20;
          if (element.tagName === 'BUTTON') confidence += 10;
          
          return Math.min(confidence, 100);
        }
        
        // 监听状态变化
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
              const target = mutation.target;
              if (target.classList.contains('liked') || 
                  target.classList.contains('followed') ||
                  target.classList.contains('shared')) {
                
                const action = detectActionType(target, '${platform}');
                if (action) {
                  const actionData = {
                    type: action,
                    timestamp: Date.now(),
                    element: target.tagName,
                    className: target.className,
                    platform: '${platform}',
                    confidence: 95,
                    source: 'state_change'
                  };
                  
                  detectedActions.push(actionData);
                  
                  if (window.electronAPI) {
                    window.electronAPI.sendTaskAction(actionData);
                  }
                }
              }
            }
          });
        });
        
        observer.observe(document.body, {
          attributes: true,
          subtree: true,
          attributeFilter: ['class']
        });
        
        // 定期报告状态
        setInterval(function() {
          if (window.electronAPI) {
            window.electronAPI.sendStatusUpdate({
              clickCount: clickCount,
              detectedActions: detectedActions,
              timestamp: Date.now()
            });
          }
        }, 3000);
        
      })();
    `;

    this.taskWindow.webContents.executeJavaScript(script);
  }

  handleActionDetected(actionData) {
    this.detectedActions.push(actionData);
    
    // 发送到渲染进程
    if (this.mainWindow) {
      this.mainWindow.webContents.send('action-detected', actionData);
    }
    
    console.log('✅ 桌面应用检测到操作:', actionData);
  }

  setupIPC() {
    // 启动任务检测
    ipcMain.handle('start-task', async (event, { platform, url, taskTypes }) => {
      console.log(`🎯 启动任务检测 - 平台: ${platform}, URL: ${url}`);
      
      this.isDetecting = true;
      this.detectedActions = [];
      
      const taskWindow = this.createTaskWindow(platform, url);
      
      return { success: true, windowId: taskWindow.id };
    });

    // 停止任务检测
    ipcMain.handle('stop-task', async (event) => {
      this.isDetecting = false;
      
      if (this.taskWindow) {
        this.taskWindow.close();
        this.taskWindow = null;
      }
      
      return { 
        success: true, 
        detectedActions: this.detectedActions 
      };
    });

    // 获取检测结果
    ipcMain.handle('get-detection-result', async (event) => {
      return {
        detectedActions: this.detectedActions,
        isDetecting: this.isDetecting,
        timestamp: Date.now()
      };
    });
  }

  init() {
    app.whenReady().then(() => {
      // 禁用web安全限制
      session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
        callback({
          responseHeaders: {
            ...details.responseHeaders,
            'Content-Security-Policy': ['*']
          }
        });
      });

      this.createMainWindow();
      this.setupIPC();
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createMainWindow();
      }
    });
  }
}

// 启动应用
const taskDetectorApp = new TaskDetectorApp();
taskDetectorApp.init();
