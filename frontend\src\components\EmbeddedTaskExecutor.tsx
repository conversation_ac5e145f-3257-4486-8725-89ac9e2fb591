import React, { useState, useEffect, useRef } from 'react';

interface EmbeddedTaskExecutorProps {
  platform: string;
  taskTypes: string[];
  onTaskComplete: (result: any) => void;
}

export const EmbeddedTaskExecutor: React.FC<EmbeddedTaskExecutorProps> = ({
  platform,
  taskTypes,
  onTaskComplete
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [detectedActions, setDetectedActions] = useState<any[]>([]);
  const [proxyUrl, setProxyUrl] = useState('');

  useEffect(() => {
    // 设置代理URL
    const targetUrls = {
      'bilibili': 'https://www.bilibili.com',
      'douyin': 'https://www.douyin.com',
      'kuaishou': 'https://www.kuaishou.com',
      'xiaohongshu': 'https://www.xiaohongshu.com'
    };

    const targetUrl = targetUrls[platform as keyof typeof targetUrls];
    if (targetUrl) {
      // 使用我们的代理服务器
      setProxyUrl(`/api/proxy?url=${encodeURIComponent(targetUrl)}`);
    }
  }, [platform]);

  useEffect(() => {
    if (!iframeRef.current) return;

    // 注入检测脚本到iframe
    const injectDetectionScript = () => {
      const iframe = iframeRef.current;
      if (!iframe || !iframe.contentWindow) return;

      try {
        const script = iframe.contentDocument?.createElement('script');
        if (script) {
          script.textContent = `
            // 同域检测脚本 - 无跨域限制
            (function() {
              let clickCount = 0;
              let detectedActions = [];
              
              // 监听所有点击事件
              document.addEventListener('click', function(e) {
                clickCount++;
                
                const target = e.target;
                const action = detectActionType(target);
                
                if (action) {
                  detectedActions.push({
                    type: action,
                    timestamp: Date.now(),
                    element: target.tagName,
                    className: target.className,
                    text: target.textContent?.slice(0, 50)
                  });
                  
                  // 发送到父窗口
                  window.parent.postMessage({
                    type: 'TASK_ACTION_DETECTED',
                    action: action,
                    clickCount: clickCount,
                    detectedActions: detectedActions
                  }, '*');
                }
              });
              
              // 检测操作类型
              function detectActionType(element) {
                const text = element.textContent?.toLowerCase() || '';
                const className = element.className?.toLowerCase() || '';
                const id = element.id?.toLowerCase() || '';
                
                // B站检测规则
                if (text.includes('点赞') || className.includes('like') || id.includes('like')) {
                  return 'like';
                }
                if (text.includes('分享') || className.includes('share') || id.includes('share')) {
                  return 'share';
                }
                if (text.includes('关注') || className.includes('follow') || id.includes('follow')) {
                  return 'follow';
                }
                if (text.includes('评论') || className.includes('comment') || id.includes('comment')) {
                  return 'comment';
                }
                
                return null;
              }
              
              // 定期报告状态
              setInterval(function() {
                window.parent.postMessage({
                  type: 'TASK_STATUS_UPDATE',
                  clickCount: clickCount,
                  detectedActions: detectedActions,
                  timestamp: Date.now()
                }, '*');
              }, 2000);
              
              console.log('✅ 同域检测脚本已注入');
            })();
          `;
          iframe.contentDocument?.head.appendChild(script);
        }
      } catch (error) {
        console.error('脚本注入失败:', error);
      }
    };

    // 监听iframe加载完成
    const handleIframeLoad = () => {
      setIsLoading(false);
      setTimeout(injectDetectionScript, 1000);
    };

    iframe.addEventListener('load', handleIframeLoad);

    return () => {
      iframe.removeEventListener('load', handleIframeLoad);
    };
  }, []);

  // 监听来自iframe的消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'TASK_ACTION_DETECTED') {
        console.log('✅ 检测到任务操作:', event.data);
        setDetectedActions(prev => [...prev, event.data]);
        
        // 检查是否完成所有任务
        const completedTasks = taskTypes.filter(task => 
          event.data.detectedActions.some((action: any) => action.type === task)
        );
        
        if (completedTasks.length === taskTypes.length) {
          onTaskComplete({
            success: true,
            completedTasks,
            totalClicks: event.data.clickCount,
            detectedActions: event.data.detectedActions
          });
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [taskTypes, onTaskComplete]);

  return (
    <div className="embedded-task-executor">
      <div className="task-header">
        <h3>📱 {platform} 任务执行器</h3>
        <div className="task-status">
          <span>需要完成: {taskTypes.join(', ')}</span>
          <span>已检测: {detectedActions.length} 个操作</span>
        </div>
      </div>

      {isLoading && (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>正在加载 {platform} 页面...</p>
        </div>
      )}

      <div className="iframe-container">
        <iframe
          ref={iframeRef}
          src={proxyUrl}
          title={`${platform} 任务页面`}
          className="task-iframe"
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
        />
      </div>

      <div className="detection-panel">
        <h4>🔍 实时检测状态</h4>
        <div className="detected-actions">
          {detectedActions.map((action, index) => (
            <div key={index} className="action-item">
              <span className="action-type">{action.action}</span>
              <span className="action-time">
                {new Date(action.timestamp).toLocaleTimeString()}
              </span>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .embedded-task-executor {
          width: 100%;
          height: 100vh;
          display: flex;
          flex-direction: column;
          background: #f5f5f5;
        }

        .task-header {
          background: white;
          padding: 15px 20px;
          border-bottom: 1px solid #e5e7eb;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .task-header h3 {
          margin: 0;
          color: #1f2937;
        }

        .task-status {
          display: flex;
          gap: 20px;
          font-size: 14px;
          color: #6b7280;
        }

        .iframe-container {
          flex: 1;
          position: relative;
          background: white;
        }

        .task-iframe {
          width: 100%;
          height: 100%;
          border: none;
        }

        .loading-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.9);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          z-index: 10;
        }

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #e5e7eb;
          border-top: 4px solid #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .detection-panel {
          background: white;
          border-top: 1px solid #e5e7eb;
          padding: 15px 20px;
          max-height: 200px;
          overflow-y: auto;
        }

        .detection-panel h4 {
          margin: 0 0 10px 0;
          color: #1f2937;
          font-size: 16px;
        }

        .detected-actions {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .action-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          background: #f0f9ff;
          border: 1px solid #0ea5e9;
          border-radius: 6px;
          font-size: 14px;
        }

        .action-type {
          font-weight: 500;
          color: #0369a1;
        }

        .action-time {
          color: #6b7280;
          font-size: 12px;
        }
      `}</style>
    </div>
  );
};
