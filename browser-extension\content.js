// 内容脚本 - 运行在目标网站页面中
console.log('🚀 社交媒体任务检测器已启动');

class TaskDetector {
  constructor() {
    this.detectedActions = [];
    this.clickCount = 0;
    this.startTime = Date.now();
    this.platform = this.detectPlatform();
    this.isActive = false;
    
    this.init();
  }

  detectPlatform() {
    const hostname = window.location.hostname;
    if (hostname.includes('bilibili.com')) return 'bilibili';
    if (hostname.includes('douyin.com')) return 'douyin';
    if (hostname.includes('kuaishou.com')) return 'kuaishou';
    if (hostname.includes('xiaohongshu.com')) return 'xiaohongshu';
    return 'unknown';
  }

  init() {
    // 监听来自主应用的消息
    window.addEventListener('message', (event) => {
      if (event.data.type === 'START_TASK_DETECTION') {
        this.startDetection(event.data.taskTypes);
      } else if (event.data.type === 'STOP_TASK_DETECTION') {
        this.stopDetection();
      }
    });

    // 监听来自扩展的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'START_DETECTION') {
        this.startDetection(message.taskTypes);
        sendResponse({ success: true });
      } else if (message.type === 'GET_DETECTION_RESULT') {
        sendResponse({
          detectedActions: this.detectedActions,
          clickCount: this.clickCount,
          platform: this.platform,
          duration: Date.now() - this.startTime
        });
      }
    });

    console.log(`✅ 任务检测器已在 ${this.platform} 平台初始化`);
  }

  startDetection(taskTypes = []) {
    if (this.isActive) return;
    
    this.isActive = true;
    this.taskTypes = taskTypes;
    this.startTime = Date.now();
    
    console.log(`🎯 开始检测任务: ${taskTypes.join(', ')}`);
    
    // 添加点击监听器
    document.addEventListener('click', this.handleClick.bind(this), true);
    
    // 添加特定平台的监听器
    this.addPlatformSpecificListeners();
    
    // 定期报告状态
    this.statusInterval = setInterval(() => {
      this.reportStatus();
    }, 2000);
  }

  stopDetection() {
    this.isActive = false;
    document.removeEventListener('click', this.handleClick.bind(this), true);
    
    if (this.statusInterval) {
      clearInterval(this.statusInterval);
    }
    
    console.log('🛑 任务检测已停止');
  }

  handleClick(event) {
    if (!this.isActive) return;
    
    this.clickCount++;
    const target = event.target;
    const action = this.detectActionType(target);
    
    if (action) {
      const actionData = {
        type: action,
        timestamp: Date.now(),
        element: target.tagName,
        className: target.className,
        text: target.textContent?.slice(0, 50),
        confidence: this.calculateConfidence(target, action)
      };
      
      this.detectedActions.push(actionData);
      
      console.log(`✅ 检测到操作: ${action}`, actionData);
      
      // 立即报告重要操作
      this.reportAction(actionData);
    }
  }

  detectActionType(element) {
    const text = element.textContent?.toLowerCase() || '';
    const className = element.className?.toLowerCase() || '';
    const id = element.id?.toLowerCase() || '';
    const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';
    
    // 通用检测规则
    const rules = {
      like: ['点赞', 'like', '赞', '👍', 'thumb', 'heart', '❤️'],
      share: ['分享', 'share', '转发', 'forward', 'repost'],
      follow: ['关注', 'follow', '订阅', 'subscribe'],
      comment: ['评论', 'comment', '回复', 'reply']
    };
    
    // 平台特定规则
    const platformRules = {
      bilibili: {
        like: ['.video-like', '.like-info', 'bili-icon-like'],
        share: ['.video-share', '.share-info', 'bili-icon-share'],
        follow: ['.follow-btn', '.subscribe-btn', 'bili-icon-follow']
      },
      douyin: {
        like: ['.like-btn', '.digg-btn', '.heart-btn'],
        share: ['.share-btn', '.forward-btn'],
        follow: ['.follow-btn', '.attention-btn']
      }
    };
    
    // 检查通用规则
    for (const [actionType, keywords] of Object.entries(rules)) {
      for (const keyword of keywords) {
        if (text.includes(keyword) || className.includes(keyword) || 
            id.includes(keyword) || ariaLabel.includes(keyword)) {
          return actionType;
        }
      }
    }
    
    // 检查平台特定规则
    const platformRule = platformRules[this.platform];
    if (platformRule) {
      for (const [actionType, selectors] of Object.entries(platformRule)) {
        for (const selector of selectors) {
          if (className.includes(selector.replace('.', '')) || 
              id.includes(selector.replace('#', ''))) {
            return actionType;
          }
        }
      }
    }
    
    return null;
  }

  calculateConfidence(element, action) {
    let confidence = 50;
    
    const text = element.textContent?.toLowerCase() || '';
    const className = element.className?.toLowerCase() || '';
    
    // 基于文本内容的置信度
    if (text.includes(action)) confidence += 30;
    
    // 基于CSS类名的置信度
    if (className.includes(action)) confidence += 20;
    
    // 基于元素类型的置信度
    if (element.tagName === 'BUTTON') confidence += 10;
    
    return Math.min(confidence, 100);
  }

  addPlatformSpecificListeners() {
    switch (this.platform) {
      case 'bilibili':
        this.addBilibiliListeners();
        break;
      case 'douyin':
        this.addDouyinListeners();
        break;
    }
  }

  addBilibiliListeners() {
    // B站特定的监听器
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes') {
          const target = mutation.target;
          if (target.classList?.contains('liked') || 
              target.classList?.contains('followed')) {
            this.handleStateChange(target);
          }
        }
      });
    });
    
    observer.observe(document.body, {
      attributes: true,
      subtree: true,
      attributeFilter: ['class']
    });
  }

  addDouyinListeners() {
    // 抖音特定的监听器
    // 监听视频切换
    const videoObserver = new MutationObserver(() => {
      // 检测新视频加载
      this.reportStatus();
    });
    
    videoObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  handleStateChange(element) {
    // 处理状态变化（如点赞按钮变色）
    const action = this.detectActionType(element);
    if (action) {
      this.detectedActions.push({
        type: action,
        timestamp: Date.now(),
        element: element.tagName,
        className: element.className,
        confidence: 90, // 状态变化的置信度较高
        source: 'state_change'
      });
    }
  }

  reportAction(actionData) {
    // 发送到扩展后台
    chrome.runtime.sendMessage({
      type: 'ACTION_DETECTED',
      data: actionData,
      platform: this.platform
    });
    
    // 发送到主应用（如果在iframe中）
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'TASK_ACTION_DETECTED',
        action: actionData.type,
        data: actionData,
        platform: this.platform
      }, '*');
    }
  }

  reportStatus() {
    const status = {
      type: 'STATUS_UPDATE',
      detectedActions: this.detectedActions,
      clickCount: this.clickCount,
      platform: this.platform,
      duration: Date.now() - this.startTime,
      isActive: this.isActive
    };
    
    // 发送到扩展后台
    chrome.runtime.sendMessage(status);
    
    // 发送到主应用
    if (window.parent !== window) {
      window.parent.postMessage(status, '*');
    }
  }
}

// 初始化检测器
const taskDetector = new TaskDetector();

// 导出到全局作用域
window.taskDetector = taskDetector;
